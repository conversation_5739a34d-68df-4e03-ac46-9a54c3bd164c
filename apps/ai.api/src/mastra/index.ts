import {
  crmAgent,
  weatherAgent,
  weatherContactWorkflow,
} from '@lisa/ai-engine';
import {
  generalConversationAgent,
  intentAgent,
  leadWorkflow,
} from '@lisa/lead-workflow';
import { Mastra } from '@mastra/core';
import { LibSQLStore } from '@mastra/libsql';
import { PinoLogger } from '@mastra/loggers';
import 'dotenv/config';

export const mastra = new Mastra({
  agents: {
    intentAgent,
    generalConversationAgent,
    weatherAgent,
    crmAgent,
  },
  workflows: {
    leadWorkflow,
    weatherContactWorkflow,
  },
  storage: new LibSQLStore({
    url: 'file:../mastra.db',
  }),
  logger: new PinoLogger({
    name: 'Mastra AI API',
    level: 'info',
  }),
});
