{"name": "@lisa/ai.api", "version": "0.0.1", "description": "AI API - Mastra-based API for AI workflows and agents", "private": true, "main": "dist/main.js", "scripts": {"build": "MASTRA_TELEMETRY_DISABLED=1 mastra build", "start": "node .mastra/output/index.mjs", "dev": "<PERSON>ra dev", "start:dev": "<PERSON>ra dev", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@lisa/ai-engine": "workspace:*", "@lisa/lead-workflow": "workspace:*", "@mastra/agui": "^1.0.3", "@mastra/core": "^0.10.7-alpha.1", "@mastra/libsql": "^0.10.3", "@mastra/loggers": "^0.10.2", "@mastra/memory": "^0.10.4", "dotenv": "^16.5.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^22.10.7", "mastra": "^0.10.7-alpha.1", "tsx": "^4.19.2", "typescript": "^5.8.3"}}