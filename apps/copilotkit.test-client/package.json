{"name": "@lisa/copilotkit.test-client", "version": "0.0.1", "description": "CopilotKit Test Client - React frontend for testing AI chat", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --max-warnings 0", "preview": "vite preview", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@copilotkit/react-core": "^1.8.10", "@copilotkit/react-ui": "^1.8.10", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.18.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.18.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.6", "globals": "^16.0.0", "typescript": "^5.8.3", "vite": "^6.0.0"}}