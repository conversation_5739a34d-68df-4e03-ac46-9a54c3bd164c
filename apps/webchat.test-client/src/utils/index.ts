import { Message, MessageType } from '../types';

export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

export const getUsernameForMessage = (type: MessageType): string => {
  switch (type) {
    case 'sent':
      return 'Você';
    case 'received':
      return 'Lisa';
    case 'system':
      return 'Sistema';
    default:
      return 'Desconhecido';
  }
};

export const replaceEmojis = (text: string): string => {
  // Simple emoji replacement - can be expanded
  return text
    .replace(/:\)/g, '😊')
    .replace(/:\(/g, '😢')
    .replace(/:D/g, '😃')
    .replace(/;\)/g, '😉')
    .replace(/<3/g, '❤️');
};

// Local storage utilities
const CHAT_HISTORY_KEY = 'lisa-webchat-history';
const MESSAGES_KEY = 'lisa-webchat-messages';

interface ChatHistory {
  environment: string;
  sessionId: string;
}

export const saveChatHistory = (history: ChatHistory): void => {
  try {
    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(history));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to save chat history:', error);
  }
};

export const loadChatHistory = (): Partial<ChatHistory> => {
  try {
    const stored = localStorage.getItem(CHAT_HISTORY_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to load chat history:', error);
    return {};
  }
};

export const saveMessageToHistory = (message: Message): void => {
  try {
    const messages = loadMessagesFromHistory();
    messages.push(message);
    // Keep only last 100 messages
    const recentMessages = messages.slice(-100);
    localStorage.setItem(MESSAGES_KEY, JSON.stringify(recentMessages));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to save message:', error);
  }
};

export const loadMessagesFromHistory = (): Message[] => {
  try {
    const stored = localStorage.getItem(MESSAGES_KEY);
    if (!stored) return [];

    const messages = JSON.parse(stored);
    // Convert time strings back to Date objects
    return messages.map((msg: unknown) => ({
      ...(msg as Message),
      time: new Date((msg as Message).time),
    }));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to load messages:', error);
    return [];
  }
};

export const clearChatHistory = (): void => {
  try {
    localStorage.removeItem(MESSAGES_KEY);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to clear chat history:', error);
  }
};
