import { Controller, Get, Res } from '@nestjs/common';
import { WebchatTestClientService } from './webchat.test-client.service';
import { Response } from 'express';
import { readFileSync } from 'fs';
import { join } from 'path';

@Controller()
export class WebchatTestClientController {
  constructor(
    private readonly webchatTestClientService: WebchatTestClientService,
  ) {}

  @Get()
  root(@Res() res: Response) {
    try {
      const htmlPath = join(
        process.cwd(),
        'apps',
        'webchat.test-client',
        'public',
        'index.html',
      );
      const html = readFileSync(htmlPath, 'utf8');
      res.setHeader('Content-Type', 'text/html');
      res.send(html);
    } catch (error) {
      console.error('Error serving HTML:', error);
      res.status(500).send(`
        <h1>Lisa WebChat Test Client</h1>
        <p>Error loading interface. Please check if files exist.</p>
        <p>Error: ${error.message}</p>
      `);
    }
  }

  @Get('health')
  getHealth(): string {
    return this.webchatTestClientService.getHealth();
  }
}
