import { NestFactory } from '@nestjs/core';
import { WebchatTestClientModule } from './webchat.test-client.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(
    WebchatTestClientModule,
  );

  // Serve static files
  const publicPath = join(
    process.cwd(),
    'apps',
    'webchat.test-client',
    'public',
  );

  app.useStaticAssets(publicPath);

  console.log('Static assets path:', publicPath);

  // Enable CORS for development
  app.enableCors({
    origin: true,
    credentials: true,
  });

  const port = process.env.PORT || 3001;
  await app.listen(port);
  console.log(`WebChat Test Client is running on: http://localhost:${port}`);
}
bootstrap();
