# @lisa/ai-engine

Core AI engine for Lisa workspace - agents, workflows, and tools powered by <PERSON><PERSON>.

## Overview

This library provides a structured approach to building AI-powered applications using the Mastra framework. It includes:

- **Executions**: Reusable business logic with type-safe definitions and implementations
- **Tools**: AI tools that can be used by agents
- **Workflows**: Multi-step processes with orchestrated execution
- **Agents**: AI agents with specific capabilities and instructions
- **Engine**: Core orchestration and configuration management

## Architecture

### Executions Pattern

The library uses a unique **executions pattern** that separates definitions from implementations while keeping them in the same file:

```typescript
// Definitions with schemas
export const weatherToolDefinition = {
  id: 'weather-tool',
  description: 'Fetches weather information',
  inputSchema: z.object({ city: z.string() }),
  outputSchema: z.object({ temperature: z.number() }),
} as const;

// Derived types
export type WeatherToolInput = z.infer<typeof weatherToolDefinition.inputSchema>;
export type WeatherToolOutput = z.infer<typeof weatherToolDefinition.outputSchema>;

// Typed execution function
export const weatherToolExecution = async ({ context }: { context: WeatherToolInput }): Promise<WeatherToolOutput> => {
  // Implementation here
};
```

This pattern provides:
- **Type Safety**: Full TypeScript support with inferred types
- **Reusability**: Same logic can be used in tools and workflow steps
- **Maintainability**: Definitions and implementations stay together
- **Consistency**: Standardized structure across all executions

## Usage

### Basic Setup

```typescript
import { getOrchestrator } from '@lisa/ai-engine';

// Get the orchestrator instance
const orchestrator = getOrchestrator({
  logger: { name: 'MyApp', level: 'info' }
});

// Use an agent
const response = await orchestrator.generateFromAgent(
  'weatherAgent', 
  'What is the weather in São Paulo?'
);

// Execute a workflow
const result = await orchestrator.executeWorkflow('weatherContactWorkflow', {
  city: 'São Paulo',
  contactName: 'John Doe',
  contactEmail: '<EMAIL>'
});
```

### Available Components

#### Agents
- `weatherAgent`: Provides weather information
- `crmAgent`: Manages customer contacts

#### Tools
- `weatherTool`: Get weather data for a city
- `createContactTool`: Create new CRM contacts
- `updateContactTool`: Update existing contacts

#### Workflows
- `weatherContactWorkflow`: Get weather and create contact

#### Steps
- `weatherStep`: Weather data step for workflows
- `createContactStep`: Contact creation step for workflows

## Development

### Building
```bash
pnpm build
```

### Testing
```bash
pnpm test
```

### Type Checking
```bash
pnpm type-check
```

## Adding New Executions

1. Create a new file in `src/executions/[feature].ts`
2. Define your schemas and types
3. Implement typed execution functions
4. Create tools/steps that use the executions
5. Export everything through the appropriate index files

Example:
```typescript
// src/executions/email.ts
export const sendEmailToolDefinition = {
  id: 'send-email-tool',
  description: 'Sends an email',
  inputSchema: z.object({
    to: z.string().email(),
    subject: z.string(),
    body: z.string(),
  }),
  outputSchema: z.object({
    messageId: z.string(),
    sent: z.boolean(),
  }),
} as const;

export type SendEmailToolInput = z.infer<typeof sendEmailToolDefinition.inputSchema>;
export type SendEmailToolOutput = z.infer<typeof sendEmailToolDefinition.outputSchema>;

export const sendEmailToolExecution = async ({ context }: { context: SendEmailToolInput }): Promise<SendEmailToolOutput> => {
  // Implementation
};
```

## License

Private - Lisa Workspace
