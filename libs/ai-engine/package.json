{"name": "@lisa/ai-engine", "version": "0.0.1", "description": "Core AI engine for Lisa workspace - agents, workflows, and tools powered by Mastra", "private": true, "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"build": "tsup", "build:tsc": "tsc -p tsconfig.lib.json", "build:watch": "tsup --watch", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "lint": "eslint src/**/*.ts --max-warnings 50", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@mastra/core": "^0.10.7-alpha.1", "@mastra/loggers": "^0.10.2", "@ai-sdk/openai": "^1.3.17", "zod": "^3.25.56"}, "devDependencies": {"@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "tsup": "^8.5.0", "typescript": "^5.8.3", "eslint": "^9.28.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0"}, "peerDependencies": {"@lisa/common": "workspace:*"}}