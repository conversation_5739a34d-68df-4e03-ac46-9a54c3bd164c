import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';

// Import agents
import { crmAgent, weatherAgent } from '../agents';

// Import workflows
import { weatherContactWorkflow } from '../workflows';

// Engine configuration interface
export interface EngineConfig {
  logger?: {
    name?: string;
    level?: 'debug' | 'info' | 'warn' | 'error';
  };
  storage?: any; // Mastra storage provider
  serverMiddleware?: Array<{
    handler: (c: any, next: () => Promise<void>) => Promise<void>;
  }>;
}

// Default configuration
export const defaultEngineConfig: EngineConfig = {
  logger: {
    name: 'Lisa AI Engine',
    level: 'info',
  },
};

// Create Mastra instance with configuration
export function createMastraEngine(config: EngineConfig = {}) {
  const finalConfig = { ...defaultEngineConfig, ...config };

  return new Mastra({
    // Agents
    agents: {
      weatherAgent,
      crmAgent,
    },

    // Workflows
    workflows: {
      weatherContactWorkflow,
    },

    // Logger
    logger: new PinoLogger({
      name: finalConfig.logger?.name || 'Lisa AI Engine',
      level: finalConfig.logger?.level || 'info',
    }),

    // Storage (if provided)
    ...(config.storage && { storage: config.storage }),

    // Server middleware (if provided)
    ...(config.serverMiddleware && {
      serverMiddleware: config.serverMiddleware,
    }),
  });
}

// Export types are already exported above
