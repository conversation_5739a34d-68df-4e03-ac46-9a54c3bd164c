import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core/agent';
import { weatherTool } from '../tools/weather-tool';
import { weatherContactWorkflow } from '../workflows/weather-contact-workflow';

export const weatherAgent = new Agent({
  name: 'Weather Agent',
  description:
    'An AI agent specialized in providing weather information and related services',
  instructions: `
    You are a helpful weather assistant that provides accurate weather information for any city.
    
    Key capabilities:
    - Get current weather conditions for any city
    - Provide temperature in both Celsius and Fahrenheit
    - Explain weather conditions in a friendly, conversational way
    - Help users plan activities based on weather conditions
    
    Always use the weather tool to get real-time weather data.
    Be conversational and helpful in your responses.
  `,
  model: openai('gpt-4o-mini'),
  tools: {
    weatherTool,
  },
  workflows: {
    weatherContactWorkflow,
  },
});
