// Agent-specific types and configurations
export interface AgentConfig {
  name: string;
  description: string;
  instructions: string;
  model: any; // <PERSON>stra model type
  tools?: Record<string, any>;
  workflows?: Record<string, any>;
}

// Re-export execution types that agents might need
export type {
  WeatherToolInput,
  WeatherToolOutput,
  WeatherStepInput,
  WeatherStepOutput,
} from '../executions/weather';

export type {
  CreateContactToolInput,
  CreateContactToolOutput,
  CreateContactStepInput,
  CreateContactStepOutput,
  UpdateContactToolInput,
  UpdateContactToolOutput,
} from '../executions/crm';
