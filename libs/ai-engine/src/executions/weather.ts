import { z } from 'zod';

// ✅ Definições exportadas
export const weatherToolDefinition = {
  id: 'weather-tool',
  description: 'Fetches weather information for a given city',
  inputSchema: z.object({
    city: z.string().describe('The city to get weather for'),
    units: z.enum(['celsius', 'fahrenheit']).optional().default('celsius'),
  }),
  outputSchema: z.object({
    city: z.string(),
    temperature: z.number(),
    condition: z.string(),
    humidity: z.number(),
  }),
} as const;

export const weatherStepDefinition = {
  id: 'weather-step',
  description: 'Step to get weather data in workflow',
  inputSchema: z.object({
    city: z.string(),
    units: z.enum(['celsius', 'fahrenheit']).optional(),
  }),
  outputSchema: z.object({
    weatherData: z.object({
      city: z.string(),
      temperature: z.number(),
      condition: z.string(),
    }),
  }),
} as const;

// ✅ Types derivados das definições
export type WeatherToolInput = z.infer<typeof weatherToolDefinition.inputSchema>;
export type WeatherToolOutput = z.infer<typeof weatherToolDefinition.outputSchema>;
export type WeatherStepInput = z.infer<typeof weatherStepDefinition.inputSchema>;
export type WeatherStepOutput = z.infer<typeof weatherStepDefinition.outputSchema>;

// ✅ Execuções tipadas
export const weatherToolExecution = async ({ context }: { context: WeatherToolInput }): Promise<WeatherToolOutput> => {
  const { city, units = 'celsius' } = context;
  
  // Lógica compartilhada
  const weatherData = await fetchWeatherData(city, units);
  
  return {
    city,
    temperature: weatherData.temp,
    condition: weatherData.condition,
    humidity: weatherData.humidity,
  };
};

export const weatherStepExecution = async ({ inputData }: { inputData: WeatherStepInput }): Promise<WeatherStepOutput> => {
  const { city, units = 'celsius' } = inputData;
  
  // Reutiliza a mesma lógica
  const weatherData = await fetchWeatherData(city, units);
  
  return {
    weatherData: {
      city,
      temperature: weatherData.temp,
      condition: weatherData.condition,
    },
  };
};

// ✅ Funções auxiliares privadas
async function fetchWeatherData(city: string, units: string) {
  // TODO: Implementar integração com API de clima real
  // Por enquanto, retorna dados mock
  const mockData = {
    temp: units === 'fahrenheit' ? 77 : 25,
    condition: 'sunny' as const,
    humidity: 60,
  };
  
  // Simula delay de API
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return mockData;
}
