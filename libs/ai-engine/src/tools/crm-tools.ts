import { createTool } from '@mastra/core/tools';
import { 
  createContactToolDefinition, 
  createContactToolExecution,
  updateContactToolDefinition,
  updateContactToolExecution
} from '../executions/crm';

export const createContactTool = createTool({
  ...createContactToolDefinition,
  execute: createContactToolExecution,
});

export const updateContactTool = createTool({
  ...updateContactToolDefinition,
  execute: updateContactToolExecution,
});
