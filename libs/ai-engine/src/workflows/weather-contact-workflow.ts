import { createWorkflow } from '@mastra/core/workflows';
import { z } from 'zod';

// Workflow simples para demonstração
export const weatherContactWorkflow = createWorkflow({
  id: 'weather-contact-workflow',
  description: 'Simple workflow example',
  inputSchema: z.object({
    city: z.string().describe('City to get weather for'),
    contactName: z.string().describe('Name of the contact to create'),
    contactEmail: z.string().email().describe('Email of the contact to create'),
    contactPhone: z.string().optional().describe('Phone of the contact'),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
});

// TODO: Implementar steps quando a API do Mastra estiver mais estável
// Por enquanto, deixamos o workflow sem steps para evitar erros de compatibilidade
