// Core engine
export * from './engine';

// Executions (definitions, types, and execution functions)
export * from './executions';

// Tools
export * from './tools';

// Workflows
export * from './workflows';

// Agents
export * from './agents';

// Utilities
export * from './utils';

// Main exports for easy access
export {
  AIEngineOrchestrator,
  // Engine
  createMastraEngine,
  getOrchestrator,
} from './engine';

export {
  createContactTool,
  updateContactTool,
  // Tools
  weatherTool,
} from './tools';

export {
  crmAgent,
  // Agents
  weatherAgent,
} from './agents';

export {
  createContactStep,
  // Workflows
  weatherContactWorkflow,

  // Steps
  weatherStep,
} from './workflows';
