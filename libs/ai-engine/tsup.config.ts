import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['esm', 'cjs'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  outDir: 'dist',
  target: 'es2022',
  minify: false,
  bundle: true,
  external: [
    '@mastra/core',
    '@mastra/loggers',
    '@ai-sdk/openai',
    'zod',
    '@lisa/common',
  ],
  tsconfig: './tsconfig.lib.json',
});
